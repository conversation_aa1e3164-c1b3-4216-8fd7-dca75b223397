<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
        <relativePath/>
    </parent>

    <groupId>com.mall</groupId>
    <artifactId>mall</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>mall</name>
    <description>基于Spring Cloud Alibaba的微服务电商系统</description>

    <modules>
        <module>mall-gateway</module>
        <module>mall-common</module>
        <module>mall-project</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <spring-cloud-alibaba.version>2023.0.3.3</spring-cloud-alibaba.version>
        <spring-cloud.version>2025.0.0</spring-cloud.version>
        <mysql.version>8.3.0</mysql.version>
        <lombok.version>1.18.32</lombok.version>
        <jakarta-validation.version>3.0.2</jakarta-validation.version>
        <jjwt.version>0.11.5</jjwt.version>
        <hutool.version>5.8.25</hutool.version>
        <zxing.version>3.5.1</zxing.version>
        <tencentcloud.version>3.1.270</tencentcloud.version>
        <easyexcel.version>3.3.4</easyexcel.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 1. Spring Cloud 官方依赖管理 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 2. Spring Cloud Alibaba 依赖管理 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 其他依赖管理 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mall</groupId>
                <artifactId>mall-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 添加 Jakarta Validation 提供注解驱动的数据验证（如 @NotNull, @Size, @Email 等） -->
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta-validation.version}</version>
            </dependency>

            <!-- Lombok 版本管理 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            
            <!-- JWT依赖管理 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            
            <!-- 工具类库管理 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            
            <!-- 二维码生成库管理 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            
            <!-- 腾讯云SDK管理 -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud.version}</version>
            </dependency>
            
            <!-- EasyExcel管理 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
