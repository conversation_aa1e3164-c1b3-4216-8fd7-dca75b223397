package com.mall.project.service.statusSet.impl;

import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.statusSet.StatusSetDao;
import com.mall.project.dto.statusSet.StatusSet;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.quantizationValue.impl.QuantizationValueServiceImpl;
import com.mall.project.service.statusSet.StatusSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isEmpty;
import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 状态设置服务实现类
 */
@Service
@Slf4j
public class StatusSetServiceImpl implements StatusSetService {

    @Autowired
    private StatusSetDao statusSetDao;

    @Autowired
    private QuantizationValueServiceImpl quantizationValueService;

    /**
     * 保存或更新状态设置
     */
    @Override
    public Map<String, Object> saveOrUpdateStatusSet(StatusSet pojo, Integer updatePerson) {
        if(isEmpty(pojo.getIsEnabled()) || !pojo.getIsEnabled().matches("^[01]$")) {
            throw new BusinessException("状态设置开、关不能为空，且只能为0或1");
        }
        if(isEmpty(pojo.getType()) || !pojo.getType().matches("^[01]$")) {
            throw new BusinessException("状态设置类型不能为空，且只能为0或1");
        }
        int result =  statusSetDao.saveOrUpdateStatusSet(pojo,updatePerson);
        if(result > 0){
            return getStatusSet(pojo.getType());
        }else{
            return null;
        }
    }
    /**
     * 查询状态设置
     */
    @Override
    public Map<String, Object> getStatusSet(String type) {
        return statusSetDao.getStatusSet(type);
    }

    /**
     * 用户状态为不正常的用户, 当日的量化值,补贴金统计
     */
    @Override
    public void updateQuantifyAndSubsidy() {
        // 获取当日量化率
        BigDecimal quantizationRate = new BigDecimal(quantizationValueService.getLatestQuantizationRate(""));
        //流失的量化值 = B,CB用户状态不正常的量化数的累计量化数 乘以 当日量化率
        //String quantifyValue = new BigDecimal(statusSetDao.quantizationValue()).multiply(quantizationRate).setScale(2, RoundingMode.DOWN).toString();
        BigDecimal quantifyValue = new BigDecimal("0");
        List<Map<String, Object>> abnormalQuantifyCount = statusSetDao.quantizationValue();
        for (Map<String, Object> dataMap : abnormalQuantifyCount) {   // 循环遍历B,CB 用户状态不正常的累计量化数
            BigDecimal quantifyCount = new BigDecimal(dataMap.get("quantify_count").toString());
            BigDecimal value = quantifyCount.multiply(quantizationRate).setScale(2, RoundingMode.DOWN);
            quantifyValue = quantifyValue.add(value);
        }
        String lostSubsidy = statusSetDao.lostSubsidy();          //C用户状态为不正常流失的补贴金
        statusSetDao.updateQuantifyAndSubsidy(quantifyValue.toString(),lostSubsidy);
    }

    /**
     * 把量化数表中的状态数 号码 = *********** 添加到状态数的量化值表中
     */
    @Override
    public void updateQuantifyToStatusSet() {
        // 获取当日量化率
        BigDecimal quantizationRate = new BigDecimal(quantizationValueService.getLatestQuantizationRate(""));
        BigDecimal statusQuantify = statusSetDao.getStatusQuantify();  // 量化数表中的状态数 号码 = ***********
        BigDecimal value = statusQuantify.multiply(quantizationRate).setScale(2, RoundingMode.DOWN);
        statusSetDao.updateQuantifyToStatusSet(value);
    }

    /**
     * 量化值 统计
     */
    @Override
    public String quantizationValue() {
        // 查询量化值,如果为null,则返回0
        Map<String, Object> quantify = statusSetDao.getQuantifyAndSubsidy();
        if(quantify == null || quantify.isEmpty()){
            return "0";
        }
        if(quantify.get("quantify") == null){
            return "0";
        }
        return quantify.get("quantify").toString();
    }

    /**
     * 量化值 输出
     */
    @Override
    public int outputQuantizationValue(String phone, String quantifyValue, int updatePerson) {
        // 验证手机号格式
        if(isEmpty(phone)) {
            throw new BusinessException("手机号不能为空");
        }
        if(isNotEmpty(phone) && !phone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }
        if(isEmpty(quantifyValue)) {
            throw new BusinessException("量化值不能为空");
        }
        //量化值只能为正整数或小数，且最多保留两位小数
        if(isNotEmpty(quantifyValue) && !quantifyValue.matches("^\\d+(\\.\\d{1,2})?$")) {
            throw new BusinessException("量化值只能为正整数或小数，且最多保留两位小数");
        }
        // 检查用户输入的量化值是否大于用户当前的量化值,如大于则抛出异常
        if(isNotEmpty(quantifyValue) && isNotEmpty(phone)) {
            String currentQuantifyValue = quantizationValue();
            if(isNotEmpty(currentQuantifyValue) && new BigDecimal(quantifyValue).compareTo(new BigDecimal(currentQuantifyValue)) > 0) {
                throw new BusinessException("输出的量化值不能大于当前系统的量化值");
            }
        }
        return statusSetDao.outputQuantizationValue(phone,quantifyValue,updatePerson);
    }
    /**
     * 流失的 补贴金 统计
     */
    @Override
    public String lostSubsidy() {
        Map<String, Object> subsidy = statusSetDao.getQuantifyAndSubsidy();
        if(subsidy == null || subsidy.isEmpty()){
            return "0";
        }
        if(subsidy.get("subsidy") == null){
            return "0";
        }
        return subsidy.get("subsidy").toString();
    }
    /**
     * 补贴金 输出
     */
    @Override
    public int outputSubsidy(String phone, String subsidy, int updatePerson) {
        // 验证手机号格式
        if(isEmpty(phone)) {
            throw new BusinessException("手机号不能为空");
        }
        if(isNotEmpty(phone) && !phone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }
        if(isEmpty(subsidy)) {
            throw new BusinessException("补贴金不能为空");
        }
        //补贴金只能为正整数或小数，且最多保留两位小数
        if(isNotEmpty(subsidy) && !subsidy.matches("^\\d+(\\.\\d{1,2})?$")) {
            throw new BusinessException("补贴金只能为正整数或小数，且最多保留两位小数");
        }
        // 检查用户输入的补贴金是否大于用户当前的补贴金,如大于则抛出异常
        if(isNotEmpty(subsidy) && isNotEmpty(phone)) {
            String currentSubsidy = lostSubsidy();
            if(isNotEmpty(currentSubsidy) && new BigDecimal(subsidy).compareTo(new BigDecimal(currentSubsidy)) > 0) {
                throw new BusinessException("输出的补贴金不能大于当前系统的补贴金");
            }
        }
        return statusSetDao.outputSubsidy(phone,subsidy,updatePerson);
    }

    /**
     * 读取企业
     */
    @Override
    public Map<String, Object> queryEnterprise() {
        List<Map<String, Object>> lsitMap = statusSetDao.queryEnterprise();
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = lsitMap.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
        Map<String,Object> mapEnterprise = new HashMap<>();
        mapEnterprise.put("cooperateEnterprise", formattedTradeDataSet);
        return mapEnterprise;
    }

    /**
     * 查询企业是否存在手机号
     */
    @Override
    public boolean checkPhoneExists(String phone, String tableName) {
        return statusSetDao.checkPhoneExists(phone, tableName);
    }
}
