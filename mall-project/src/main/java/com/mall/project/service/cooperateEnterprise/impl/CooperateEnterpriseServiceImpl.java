package com.mall.project.service.cooperateEnterprise.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.cooperateEnterprise.CooperateEnterpriseDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.cooperateEnterprise.CooperateEnterpriseService;
import com.mall.project.util.MallBAuthUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

@Service
@Slf4j
public class CooperateEnterpriseServiceImpl implements CooperateEnterpriseService {

    @Autowired
    private CooperateEnterpriseDao cooperateEnterpriseDao;
    
    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 获取所有合作企业信息列表
     * <p>通过数据访问层获取全部合作企业的基础信息，企业信息以键值对形式存储在Map中，
     * 每个Map对应一个企业的完整字段信息，字段名作为键，字段值作为对应的值</p>
     *
     * @return List<Map<String, Object>> 合作企业信息集合，每个元素代表一个企业的完整信息。
     *         当无数据时返回空集合（非null）
     */
    public Map<String, Object> getAllCooperateEnterprise() {
        // 调用DAO层方法获取全量数据，底层实现包含企业信息的标准化转换逻辑
        List<Map<String, Object>> cooperateEnterprises = cooperateEnterpriseDao.getAllCooperateEnterprise();
        // 转换下划线格式为驼峰格式并格式化时间
        List<Map<String, Object>> formattedTradeDataSet = cooperateEnterprises.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
        Map<String, Object> mapEnterprise = new HashMap<>();
        mapEnterprise.put("cooperateEnterprises", formattedTradeDataSet);
        mapEnterprise.put("total", cooperateEnterprises.size());
        return mapEnterprise;
    }

    @Override
    public int addEnterpriseDataSet(Long enterpriseId) {
        if (enterpriseId == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        int result = cooperateEnterpriseDao.addEnterpriseDataSet(enterpriseId);
        if (result > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public int deleteEnterpriseDataSet(Long enterpriseId) {
        if (enterpriseId == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        int result = cooperateEnterpriseDao.deleteEnterpriseDataSet(enterpriseId);
        if(result > 0){
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 查看已设置参数的企业信息
     */
    @Override
    public Map<String, Object> getEnterpriseDataSet() {
        List<Map<String, Object>> cooperateEnterprise = cooperateEnterpriseDao.getEnterpriseDataSet();
        Map<String,Object> mapEnterprise = new HashMap<>();
        if(cooperateEnterprise.isEmpty()){
            mapEnterprise.put("cooperateEnterprise", List.of());
            return mapEnterprise;
        }
        List<Map<String, Object>> formattedTradeDataSet = cooperateEnterprise.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());

        mapEnterprise.put("cooperateEnterprise", formattedTradeDataSet);
        return mapEnterprise;
    }

    /**
     * 删除企业交易数据
     */
    @Override
    public int deleteEnterpriseDataName(Long enterpriseProductDataId) {
        if (enterpriseProductDataId == null){
            throw new BusinessException("交易数据ID不能为空");
        }
        return cooperateEnterpriseDao.deleteEnterpriseDataName(enterpriseProductDataId);
    }

    /**
     * 今日总交易量
     */
    @Override
    public String todayTotalTrade(int type){
        return cooperateEnterpriseDao.todayTotalTrade(type);
    }

    /**
     * 今日总合计数
     */
    @Override
    public String todayTotalCount(int type){
        return cooperateEnterpriseDao.todayTotalCount(type);
    }

    /**
     * 累计总交易量
     */
    @Override
    public String cumulativeTotalTrade(int type){
        return cooperateEnterpriseDao.cumulativeTotalTrade(type);
    }

    /**
     * 设置自定义常数
     */
    @Override
    public int setCustomConstants(String customConstants) {
        if (customConstants == null){
            throw new BusinessException("参数不能为空");
        }
        return cooperateEnterpriseDao.setCustomConstants(customConstants);
    }

    /**
     * 查询自定义常数
     */
    @Override
    public Map<String, Object> getCustomConstants() {
        Map<String, Object> customConstants = cooperateEnterpriseDao.getCustomConstants();
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.convertToCamelCase(customConstants);
    }

    /**
     * 添加各企业系统每日更新总累计量化数企业名称
     */
    @Override
    public int addEnterpriseQuantitySet(Long enterpriseId) {
        if (enterpriseId == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        int result = cooperateEnterpriseDao.addEnterpriseQuantitySet(enterpriseId);
        if (result > 0) {
            return 1;
        }else{
            return 0;
        }
    }

    /**
     * 删除已添加各企业系统每日更新总累计量化数企业名称
     */
    @Override
    public int deleteEnterpriseQuantitySet(Long enterpriseId) {
        if (enterpriseId == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        return cooperateEnterpriseDao.deleteEnterpriseQuantitySet(enterpriseId);
    }

    /**
     * 查看已添加各企业系统每日更新总累计量化数企业名称
     */
    @Override
    public Map<String, Object> getEnterpriseQuantitySet() {
        List<Map<String, Object>> cooperateEnterprise = cooperateEnterpriseDao.getEnterpriseQuantitySet();
        if(cooperateEnterprise.isEmpty()){
            throw new BusinessException("暂无数据");
        }
        List<Map<String, Object>> formattedTradeDataSet = cooperateEnterprise.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
        Map<String,Object> mapEnterprise = new HashMap<>();
        mapEnterprise.put("cooperateEnterprise", formattedTradeDataSet);
        return mapEnterprise;
    }

    /**
     * 删除各企业系统每日更新总累计量化数交易数据
     */
    @Override
    public int deleteEnterpriseQuantityName(Long enterpriseProductDataId) {
        if (enterpriseProductDataId == null){
            throw new BusinessException("交易数据ID不能为空");
        }
        return cooperateEnterpriseDao.deleteEnterpriseQuantityName(enterpriseProductDataId);
    }

    /**
     * 计算量化率 quantizationRate
     */
    @Override
    public Map<String,Object> quantizationRate(String searchMonth) {
        // 在调用Dao层方法前进行参数校验 判断格式为  yyyy-MM
        if (searchMonth != null && !searchMonth.isEmpty() && !searchMonth.matches("\\d{4}-\\d{2}")) {
            throw new BusinessException("查询量化率月份格式不正确，请输入yyyy-MM格式");
        }
        List<Map<String, Object>> currentMonthQuantizationRateList = cooperateEnterpriseDao.quantizationRate(searchMonth);
        Map<String, Object> currentMonthQuantizationRateMap = new HashMap<>();   // 当前月量化率
        if(isNotEmpty(currentMonthQuantizationRateList)){
            Map<String, Object> bSettings = cooperateEnterpriseDao.getBSettings();   // 合作企业各IDB设置 量化值进化量化 和 量化进化平台促销券
            currentMonthQuantizationRateMap.put("currentMonthQuantizationRate", currentMonthQuantizationRateList);    // 量化率
            currentMonthQuantizationRateMap.put("quantifyToCredit", bSettings.get("quantify_to_credit").toString());  // 量化值进化量化
            currentMonthQuantizationRateMap.put("creditToCoupon", bSettings.get("credit_to_coupon").toString());      // 量化进化平台促销券
        }
        return currentMonthQuantizationRateMap;
    }
    /**
     * 交易数据设置
     */
    @Override
    public int tradeDataSet(Long enterpriseId, String tradeName) {
        // 分别进行参数校验
        if (enterpriseId == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        if (tradeName == null || tradeName.isEmpty()){
            throw new BusinessException("交易名称不能为空");
        }
        int result =  cooperateEnterpriseDao.tradeDataSet(enterpriseId, tradeName);
        if(result == 0){
            throw new BusinessException("数据名称已存在,不能重复添加");
        }
        return 1;
    }

    /**
     * 查询交易数据设置
     */
    @Override
    public Map<String, Object> tradeDataSet() {
        List<Map<String, Object>> tradeDataSet = cooperateEnterpriseDao.tradeDataSet();
        if(tradeDataSet.isEmpty()){
            throw new BusinessException("暂无数据");
        }
        // 转换下划线格式为驼峰格式并格式化时间
        List<Map<String, Object>> formattedTradeDataSet = tradeDataSet.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
        Map<String, Object> tradeDataSetMap = new HashMap<>();
        tradeDataSetMap.put("tradeDataSet", formattedTradeDataSet);
        return tradeDataSetMap;
    }
    /**
     * 删除数据名称
     */
    @Override
    public int deleteTradeDataSet(Long id) {
        if (id == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        return cooperateEnterpriseDao.deleteTradeDataSet(id);
    }

    /**
     * 设置交易数据的每笔交易金额
     */
    @Override
    public int tradeDataParameterSet(Long enterpriseId, String perTradeAmount,String onOff){
        if (enterpriseId == null){
            throw new BusinessException("合作企业ID不能为空");
        }
        if (perTradeAmount == null || perTradeAmount.isEmpty()){
            throw new BusinessException("每笔交易金额不能为空");
        }
        // 验证每笔交易金额格式
        if (!perTradeAmount.matches("^[1-9]\\d*(\\.\\d+)?$")) {
            throw new BusinessException("每笔交易金额格式错误，请输入正数");
        }
        return cooperateEnterpriseDao.tradeDataParameterSet(enterpriseId, perTradeAmount,onOff);
    }
    /**
     * 查询设置交易数据的每笔交易金额
     */
    @Override
    public Map<String, Object> QueryTradeDataParameterSet(){
        Map<String, Object> dataMap = cooperateEnterpriseDao.QueryTradeDataParameterSet();
        if (dataMap == null || dataMap.isEmpty()){
            throw new BusinessException("暂无数据");
        }
        return ConvertToCamelCase.convertToCamelCase(dataMap);
    }

    /**
     * 查询中南惠 交易数据明细 分页显示
     */
    @Override
    public CustomCommonPage<Map<String, Object>> QueryZNHTradeDataPages(String phone, String startTime, String endTime, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd HH:mm:ss
        if (startTime != null && !startTime.isEmpty() && !startTime.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endTime != null && !endTime.isEmpty() && !endTime.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10; // Default page size
        }

        int offset = (pageNum - 1) * pageSize;

        List<Map<String, Object>> list = cooperateEnterpriseDao.QueryZNHTradeDataPages(phone,  startTime, endTime, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = list.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());

        long total = cooperateEnterpriseDao.countZNHTradeDatas(phone, startTime, endTime);

        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        // 获取交易总额和总计数
        double sumTradeAmount = cooperateEnterpriseDao.sumTradeAmount(phone, startTime);
        double sumTotalCount = cooperateEnterpriseDao.sumTotalCount(phone, startTime);

        CustomCommonPage<Map<String, Object>> commonPage = new CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("sumTradeAmount", sumTradeAmount);
        summary.put("sumTotalCount", sumTotalCount);
        commonPage.setSummary(summary);

        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 去mallB 系统读取每日的交易数据
     */
    @Override
    public void getTradeDataFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();
            // 读取数据类型
            String type = cooperateEnterpriseDao.queryTradeDataSet();

            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("productTypes", type);   // 数据类型 1：权限开通，2：平台广告, 3.技术引流 4. 开通代销 5. 商品推荐 6. 量化权限

            // 使用工具类发送带参数的GET请求到mallB系统获取交易数据
            ResponseEntity<String> dealDataResponse = mallBAuthUtils.getForEntityWithParams("/mall/receptionA/getDealData", requestParams, String.class);

            // 检查获取交易数据是否成功
            if (dealDataResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB系统交易数据失败: " + dealDataResponse.getStatusCode());
            }

            // 解析交易数据响应
            String dealDataResponseBody = dealDataResponse.getBody();
            if (dealDataResponseBody == null) {
                throw new BusinessException("获取mallB系统交易数据响应为空");
            }

            // 解析响应JSON
            JsonNode dealDataRoot = objectMapper.readTree(dealDataResponseBody);

            if (dealDataRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB系统交易数据失败: " + dealDataRoot.get("msg").asText());
            }

            // 获取交易数据列表
            JsonNode dealDataList = dealDataRoot.path("data");
            if (!dealDataList.isArray() || dealDataList.isEmpty()) {
                log.info("今日mallB系统无交易数据");
                return;
            }
            // 存储交易数据到本地系统
            log.info("开始处理并存储mallB交易数据，共 {} 条记录", dealDataList.size());

            // 遍历交易数据并存储
            for (JsonNode dealItem : dealDataList) {
                try {
                    Map<String, Object> dealData = new HashMap<>();

                    dealData.put("enterprise_id", 1);
                    dealData.put("company_name", dealItem.path("businessName").asText() == null ? "" : dealItem.path("businessName").asText());
                    dealData.put("phone", dealItem.path("phone").asText());
                    if(dealItem.path("productType").asInt() == 1){
                        dealData.put("trade_name", "权限开通");
                    }else if(dealItem.path("productType").asInt() == 2){
                        dealData.put("trade_name", "平台广告");
                    }else if(dealItem.path("productType").asInt() == 3){
                        dealData.put("trade_name", "技术引流");
                    }else if(dealItem.path("productType").asInt() == 4){
                        dealData.put("trade_name", "开通代销");
                    }else if(dealItem.path("productType").asInt() == 5){
                        dealData.put("trade_name", "商品推荐");
                    }else if(dealItem.path("productType").asInt() == 6){
                        dealData.put("trade_name", "量化权限");
                    }
                    dealData.put("trade_amount", dealItem.path("amount").asDouble());

                    // 调用DAO层存储数据
                    cooperateEnterpriseDao.saveMallBTradeData(dealData.get("enterprise_id").toString(),dealData.get("phone").toString(),dealData.get("company_name").toString(),dealData.get("trade_name").toString(),dealData.get("trade_amount").toString());
                    
                } catch (Exception e) {
                    log.error("处理mallB交易数据项失败: {}", e.getMessage(), e);
                    return;
                }
            }
            log.info("成功完成mallB系统交易数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }
    /**
     * 导出 累计交易数据 Excel
     */
    @Override
    public List<Map<String, Object>> zNHTradeDataExport(String phone, String startTime) {
        // 转换下划线格式为驼峰格式
        return cooperateEnterpriseDao.zNHTradeDataExport(phone, startTime).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }
    /**
     * 导出 获取统计今日总交易量 Excel
     */
    @Override
    public double sumTradeAmount(String phone, String startTime) {
        return cooperateEnterpriseDao.sumTradeAmount(phone, startTime);
    }
    /**
     * 导出 统计中南惠今日总合计数 Excel
     */
    @Override
    public double sumTotalCount(String phone, String startTime) {
        return cooperateEnterpriseDao.sumTotalCount(phone, startTime);
    }

    @Override
    public void updateEnterpriseTradeData() {
        cooperateEnterpriseDao.updateEnterpriseTradeData();
    }

    @Override
    public int setEnterpriseDataSwitch(String enterpriseDataSwitch) {
        return cooperateEnterpriseDao.setEnterpriseDataSwitch(enterpriseDataSwitch);
    }
    // 各企业系统每日更新总累计量化数 开关
    @Override
    public int setQuantityDataSwitch(String quantityDataSwitch) {
        return cooperateEnterpriseDao.setQuantityDataSwitch(quantityDataSwitch);
    }
    //自定义常数开、关
    @Override
    public int setCustomConstantsSwitch(String customConstantsSwitch) {
        return cooperateEnterpriseDao.setCustomConstantsSwitch(customConstantsSwitch);
    }
}


