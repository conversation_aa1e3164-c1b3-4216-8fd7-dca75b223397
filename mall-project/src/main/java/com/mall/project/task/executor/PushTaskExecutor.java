package com.mall.project.task.executor;

import com.mall.project.service.citationSettings.CitationSettingsService;
import com.mall.project.service.pushTomallB.PushTomallBService;
import com.mall.project.service.quantityconfig.QuantityConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 推送任务执行器
 * 负责执行各种向mallB系统推送数据的定时任务
 */
@Slf4j
@Component
public class PushTaskExecutor {

    @Autowired
    private QuantityConfigService quantityConfigService;

    @Autowired
    private CitationSettingsService citationSettingsService;

    @Autowired
    private PushTomallBService pushTomallBService;

    /**
     * 执行每日推送任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeDailyPushTasks() {
        log.info("开始执行每日推送任务");

        try {
            log.info("正在推送引流设置到mallB系统...");
            citationSettingsService.pushTomallB();

            log.info("正在推送分量配置到mallB系统...");
            quantityConfigService.pushTomallB();

            log.info("正在推送每日Admin量化值到mallB系统...");
            pushTomallBService.pushAdminDailyQuantifyValue();

            log.info("正在推送量化率到mallB系统...");
            pushTomallBService.pushQuantizationRate();

            log.info("正在推送B/C授权到mallB系统...");
            pushTomallBService.pushAreaAuthorize();

            log.info("正在推送量化值到mallB系统...");
            pushTomallBService.pushQuantizationValue();

            log.info("正在推送平台补贴金到mallB系统...");
            pushTomallBService.pushPlatformGold();

            log.info("每日推送任务执行完成");

        } catch (Exception e) {
            log.error("推送任务执行过程中发生错误", e);
            throw e;
        }
    }
}
