package com.mall.project.dao.quantifyCount;

import com.mall.project.dao.dailyTradePercentage.DailyTradePercentageDao;
import com.mall.project.dao.functionDatas.FunctionDatasDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 量化数计算数据访问对象
 */
@Repository
@Slf4j
public class QuantifyCountDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DailyTradePercentageDao dailyTradePercentageDao;

    @Autowired
    private FunctionDatasDao functionDatasDao;

    /**
     * 量化数设置查询
     */
    public Map<String,Object> getQuantifyCount(){
        // 对C用户量化数进行计算
        String sql = "SELECT is_enabled,proportion FROM quantify_set WHERE id = 1";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (Exception e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return new HashMap<>();
        }
    }
    /**
     * 每天的量化数计算
     */
    public void updateQuantifyCount() {
        // 对C用户量化数进行计算
        String sql = "SELECT is_enabled,proportion FROM quantify_set WHERE id = 1";
        try {
            Map<String, Object> dataMap = null;
            try {
                dataMap = jdbcTemplate.queryForMap(sql);
            } catch (EmptyResultDataAccessException e) {
                log.warn("quantify_set表中没有数据，跳过C用户量化数计算");
                dataMap = new HashMap<>();
            }

            if(dataMap != null && !dataMap.isEmpty() && dataMap.get("is_enabled") != null && dataMap.get("is_enabled").toString().equals("0")){
                // 对C用户量化数进行计算
                String proportion = dataMap.get("proportion").toString();      // 每日每ID的分量进化量化数
                sql = "UPDATE mall_b_users_count c\n" +
                        "JOIN mall_b_users u ON c.phone = u.phone\n" +
                        "SET c.quantify = CAST(c.Weight AS DECIMAL(20,2)) * ? ,\n" +               //今日量化数 = 今日分量 乘以 每日每ID的分量进化量化数 百分比
                        "c.quantify_count = CAST(c.weight_count AS DECIMAL(20,2)) * ? \n" +        //累计量化数 = 累计分量 乘以 每日每ID的分量进化量化数 百分比
                        "WHERE u.user_type = 'C'\n" +
                        "  AND c.update_time = CURDATE()";
                jdbcTemplate.update(sql, new BigDecimal(proportion).divide(new BigDecimal(100)),new BigDecimal(proportion).divide(new BigDecimal(100)));
            }

            // 对B用户量化数进行计算 方式1
            sql = "SELECT on_off FROM area_authorize_set WHERE enterprise_id = 1";   // 判断区域授权是否开启
            Map<String, Object> onOffMap = null;
            try {
                onOffMap = jdbcTemplate.queryForMap(sql);
            } catch (EmptyResultDataAccessException e) {
                log.warn("area_authorize_set表中没有数据，跳过B用户量化数计算");
                return;
            }

            sql = "SELECT on_off FROM area_proportion";                              // 判断区域授权比例是否开启
            Map<String, Object> areaProportionMap = null;
            try {
                areaProportionMap = jdbcTemplate.queryForMap(sql);
            } catch (EmptyResultDataAccessException e) {
                log.warn("area_proportion表中没有数据，跳过B用户量化数计算");
                return;
            }
            //只有区域授权和区域授权比例都开启时，才进行量化数计算
            /*if(onOffMap != null && onOffMap.get("on_off") != null &&
               areaProportionMap != null && areaProportionMap.get("on_off") != null && onOffMap.get("on_off").toString().equals("0") && areaProportionMap.get("on_off").toString().equals("0")){

                sql = "SELECT phone,area_id,start_time,end_time FROM area_authorize WHERE type = 'B'";   //只对B用户进行量化数计算
                List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql);
                for (Map<String, Object> data : dataList) {
                    String phone = data.get("phone").toString();
                    String areaId = data.get("area_id").toString();
                    String startTime = data.get("start_time").toString();
                    String endTime = data.get("end_time").toString();
                    //判断startTime 和 endTime 是否在当前时间范围内
                    boolean isInRange = jdbcTemplate.queryForObject("SELECT CASE WHEN NOW() BETWEEN ? AND ? THEN 1 ELSE 0 END AS is_in_range", Integer.class, startTime, endTime) == 1;
                    if(isInRange){
                        sql = "select u.phone from mall_b_users u where u.user_type = 'B' and town_code in(\n" +
                                "WITH RECURSIVE area_cte AS (\n" +
                                "    SELECT id\n" +
                                "    FROM area\n" +
                                "    WHERE parent_id = ? \n" +
                                "    UNION ALL\n" +
                                "    SELECT a.id\n" +
                                "    FROM area a\n" +
                                "    INNER JOIN area_cte c ON a.parent_id = c.id\n" +
                                ")\n" +
                                "SELECT * FROM area_cte)";
                        List<Map<String, Object>> phoneList= jdbcTemplate.queryForList(sql, areaId);
                        StringBuilder phoneBuilder = new StringBuilder(1024 * 1024); // 预分配1MB缓冲区
                        phoneBuilder.append(phone);
                        boolean first = false;
                        for (Map<String, Object> row : phoneList) {
                            // 从每行数据中获取 phone 值
                            String phones = String.valueOf(row.get("phone"));
                            if (!first) {
                                phoneBuilder.append(","); // 从第二个号码开始添加逗号
                            } else {
                                first = false;
                            }
                            phoneBuilder.append(phones);
                        }
                        //System.out.println("======================" + removeDuplicates(phoneBuilder.toString()));
                        String totalCountSql = "SELECT COALESCE(sum(total_count), 0) as total_count FROM enterprise_product_data where phone in("+removeDuplicates(phoneBuilder.toString())+") and enterprise_id = 1 and DATE(update_time) = CURDATE() ";

                        String totalCount = "0";
                        try {
                            Map<String, Object> totalCountMap = jdbcTemplate.queryForMap(totalCountSql);
                            Object totalCountObj = totalCountMap.get("total_count");
                            totalCount = (totalCountObj != null) ? totalCountObj.toString() : "0";
                        } catch (EmptyResultDataAccessException e) {
                            log.warn("enterprise_product_data表中没有数据，使用默认值0");
                            totalCount = "0";
                        }

                        String areaLevelSql = "select level from area_authorize z, area a where z.area_id = a.id and z.area_id = ?";
                        Map<String, Object> levelMap = null;
                        try {
                            levelMap = jdbcTemplate.queryForMap(areaLevelSql, areaId);
                        } catch (EmptyResultDataAccessException e) {
                            log.warn("area_authorize或area表中没有数据，跳过该区域的量化数计算");
                            continue;
                        }
                        if(levelMap != null && levelMap.get("level") != null) {
                            String levelSql = "";
                            String level = levelMap.get("level").toString();
                            if("1".equals(level)){
                                levelSql = "SELECT lvel1_proportion as proportion FROM area_proportion";
                            }else if("2".equals(level)){
                                levelSql = "SELECT lvel2_proportion as proportion FROM area_proportion";
                            }else if("3".equals(level)){
                                levelSql = "SELECT lvel3_proportion as proportion FROM area_proportion";
                            }else if("4".equals(level)){
                                levelSql = "SELECT lvel4_proportion as proportion FROM area_proportion";
                            }

                            if(!levelSql.isEmpty()) {
                                try {
                                    Map<String, Object> proportionMap = jdbcTemplate.queryForMap(levelSql);
                                    if(proportionMap != null && proportionMap.get("proportion") != null) {
                                        BigDecimal proportion = new BigDecimal(proportionMap.get("proportion").toString());
                                        BigDecimal quantifyCount = new BigDecimal(totalCount).multiply(proportion.divide(new BigDecimal(100)));
                                        sql = "UPDATE mall_b_users_count SET quantify = ? WHERE phone = ? and update_time = CURDATE() ";
                                        jdbcTemplate.update(sql, quantifyCount, phone);
                                    }
                                } catch (EmptyResultDataAccessException e) {
                                    log.warn("area_proportion表中没有对应级别的比例数据，跳过该区域的量化数计算");
                                }
                            }
                        }
                    }
                }
            }*/
            // 对B用户量化数进行计算 方式2
            List<Map<String, Object>> dailyTradePercentageMap = getDailyTradePercentage();
            if(isNotEmpty(dailyTradePercentageMap)){
                BigDecimal dailyTradePercentage = new BigDecimal(dailyTradePercentageMap.get(0).get("daily_trade_percentage").toString());  //所有企业各ID每日每笔新交易数据的量化数比
                String todayTotalCountsql = "select phone,total_count from enterprise_product_data WHERE DATE(update_time) = CURDATE()";    //今日交易数据 的 合计数
                List<Map<String, Object>> todayTotalCountList = jdbcTemplate.queryForList(todayTotalCountsql);
                for (Map<String, Object> data : todayTotalCountList) {
                    String phone = data.get("phone").toString();
                    String totalCount = data.get("total_count").toString(); // 今日交易数据 的 合计数
                    //1. 今日量化数 = 今日交易数据 的 合计数 乘以 所有企业各ID每日每笔新交易数据的量化数比 给自己
                    sql = "UPDATE mall_b_users_count SET quantify = quantify +? WHERE phone = ? and update_time = CURDATE() ";
                    jdbcTemplate.update(sql, new BigDecimal(totalCount).multiply(dailyTradePercentage).divide(new BigDecimal(100)), phone);
                    //2. 今日量化数 = 今日交易数据 的 合计数 乘以 排位 权限百分比 给相应排位的人
                    Integer ranking1 = Integer.parseInt(dailyTradePercentageMap.get(0).get("ranking1").toString());                             //排位1
                    BigDecimal ranking1Percentage = new BigDecimal(dailyTradePercentageMap.get(0).get("ranking1_percentage").toString());       //排位1设置获取百分比
                    String phoneByRanking1 = findPhoneByRanking(phone, ranking1);
                    if(phoneByRanking1 != null) {
                        sql = "UPDATE mall_b_users_count SET quantify = quantify +? WHERE phone = ? and update_time = CURDATE() ";
                        jdbcTemplate.update(sql, new BigDecimal(totalCount).multiply(ranking1Percentage).divide(new BigDecimal(100)), phoneByRanking1);
                    }
                    Integer ranking2 = Integer.parseInt(dailyTradePercentageMap.get(0).get("ranking2").toString());                             //排位2
                    BigDecimal ranking2Percentage = new BigDecimal(dailyTradePercentageMap.get(0).get("ranking2_percentage").toString());       //排位2设置获取百分比
                    String phoneByRanking2 = findPhoneByRanking(phone, ranking2);
                    if(phoneByRanking2 != null) {
                        sql = "UPDATE mall_b_users_count SET quantify = quantify +? WHERE phone = ? and update_time = CURDATE() ";
                        jdbcTemplate.update(sql, new BigDecimal(totalCount).multiply(ranking2Percentage).divide(new BigDecimal(100)), phoneByRanking2);
                    }
                    // 3. 通过 省,市,县、区,镇、街 授权 百分比 更新量化数
                    functionDatasDao.updateQuantifyCountByAuthorityPercentage(phone,new BigDecimal(totalCount));
                }
            }
        } catch (Exception e) {
            log.error("量化数计算失败: {}", e.getMessage(), e);
        }
    }
    /**
     *  获取历史到昨日的总量化数
     */
    public String lastDayQuantifyCount(String phone) {
        try{
            String sql = "SELECT quantify_count FROM mall_b_users_count WHERE phone = ? and update_time = CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }
    /**
     * 所有企业各ID每日每笔新交易数据的量化数比 排位 权限
     */
    public List<Map<String, Object>> getDailyTradePercentage(){
        String sql = "SELECT daily_trade_percentage,ranking1,ranking1_percentage,ranking2,ranking2_percentage FROM daily_trade_percentage WHERE is_enabled = 0";
        try {
            return jdbcTemplate.queryForList(sql);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }
    /**
     * 通过 排位 查找手机号
     */
    public String findPhoneByRanking(String phone,Integer ranking){
        try{
            String sql = "SELECT jurisdiction FROM mall_b_users WHERE phone = ?";
            // 使用 queryForList 避免 EmptyResultDataAccessException
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, phone);
            if (results.isEmpty()) {
                log.warn("未找到手机号对应的用户权限信息: {}", phone);
                return null;
            }

            // 获取用户权限
            Object jurisdictionObj = results.get(0).get("jurisdiction");
            if (jurisdictionObj == null) {
                log.warn("用户权限信息为空: {}", phone);
                return null;
            }

            int jurisdiction = Integer.parseInt(jurisdictionObj.toString());   // 原用户权限：权限1：1，权限2：2，权限3：3
            // 循环查找满足“候选权限 >= 原权限”的上级排位手机号，若最终没有符合条件的，返回 null
            while (true) {
                Map<String, Object> levelDataMap = dailyTradePercentageDao.judgeUserLevel(phone, ranking);
                if (levelDataMap == null || levelDataMap.isEmpty()) {
                    return null;    // 没有更多上级，且未找到符合条件的，返回空
                }
                int willCompareJurisdiction = Integer.parseInt(levelDataMap.get("jurisdiction").toString());
                String upRankingPhone = String.valueOf(levelDataMap.get("phone"));
                if (willCompareJurisdiction >= jurisdiction) {
                    return upRankingPhone; // 找到符合条件的立即返回
                }
                ranking += 1; // 继续往上找
            }
        }catch (Exception e){
            log.error("查询手机号失败: {}", e.getMessage(), e);
            return null;
        }
    }
    //去除逗号分隔字符串中的重复项
    public static String removeDuplicates(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 分割字符串
        String[] parts = input.split(",");

        // 使用 LinkedHashSet 保持顺序并去重
        LinkedHashSet<String> uniqueSet = new LinkedHashSet<>();
        for (String part : parts) {
            uniqueSet.add(part.trim()); // 去除空格
        }

        // 重新拼接为字符串
        return String.join(",", uniqueSet);
    }
    /**
     * 量化数设置
     */
    public int saveOrUpdateQuantifyCount(String isEnabled,String proportion,Integer updatePerson) {
        // quantify_set 表 没有数据没有数据则插入数据,有数据再更新数据 使用 ON DUPLICATE KEY UPDATE  字段有： id,is_enabled,proportion,update_person,update_time
        String sql = "INSERT INTO quantify_set(id, is_enabled, proportion, update_person, update_time) VALUES (1, ?, ?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE " +
                "is_enabled = VALUES(is_enabled), proportion = VALUES(proportion), update_person = VALUES(update_person), update_time = NOW()";
       return jdbcTemplate.update(sql,isEnabled,proportion,updatePerson);
    }

    /**
     * 量化数查询
     */
    public List<Map<String,Object>> quantifyCount(String phone,String startTime,String endTime,int limit, int offset, Integer isGreaterThanZero){
        // 参数
        List<Object> params = new ArrayList<>();
        String sql = "select * from (select c.id,DATE(c.update_time) as update_time,c.phone,u.username,c.quantify,c.Weight,c.weight_count,c.quantify_count from mall_b_users_count c,mall_b_users u\n" +
                "WHERE c.phone = u.phone\n" +
                "UNION\n" +
                "select c2.id,DATE(c2.update_time) as update_time,c2.phone,'状态数',c2.quantify,c2.Weight,c2.weight_count,c2.quantify_count from mall_b_users_count c2 where phone = '88888888888'\n" +
                ") z WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND z.phone like ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(z.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(z.update_time) <= CURDATE() ";
        }
        if (isGreaterThanZero != null && isGreaterThanZero == 1) {
            sql += " AND z.quantify > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 2) {
            sql += " AND z.weight > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 3) {
            sql += " AND z.weight_count > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 4) {
            sql += " AND z.quantify_count > 0";
        }
        sql += " ORDER BY z.update_time DESC,z.id DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     *  量化数设置 导出 Excel
     */
    public List<Map<String,Object>> exportQuantifyCountExcel(String startTime,String endTime){
        // 参数
        List<Object> params = new ArrayList<>();
        String sql = "select * from (select c.id,DATE(c.update_time) as update_time,c.phone,u.username,c.quantify,c.Weight,c.weight_count,c.quantify_count from mall_b_users_count c,mall_b_users u\n" +
                "WHERE c.phone = u.phone\n" +
                "UNION\n" +
                "select c2.id,DATE(c2.update_time) as update_time,c2.phone,'状态数',c2.quantify,c2.Weight,c2.weight_count,c2.quantify_count from mall_b_users_count c2 where phone = '88888888888'\n" +
                ") z WHERE 1=1";

        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(z.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(z.update_time) <= CURDATE() ";
        }
        sql += " ORDER BY z.update_time DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 总量化数条数
     */
    public int totalQuantifyCount(String phone,String startTime,String endTime , Integer isGreaterThanZero){
        try {
            // 参数
            List<Object> params = new ArrayList<>();
            String sql = "select count(1) from (select DATE(c.update_time) as update_time,c.phone,u.username,c.quantify,c.Weight,c.weight_count,c.quantify_count from mall_b_users_count c,mall_b_users u\n" +
                    "WHERE c.phone = u.phone\n" +
                    "UNION\n" +
                    "select DATE(c2.update_time) as update_time,c2.phone,'状态数',c2.quantify,c2.Weight,c2.weight_count,c2.quantify_count from mall_b_users_count c2 where phone = '88888888888'\n" +
                    ") z WHERE 1=1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND z.phone like ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(z.update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(z.update_time) <= CURDATE() ";
            }
            if (isGreaterThanZero != null && isGreaterThanZero == 1) {
                sql += " AND z.quantify > 0";
            }else if (isGreaterThanZero != null && isGreaterThanZero == 2) {
                sql += " AND z.weight > 0";
            }else if (isGreaterThanZero != null && isGreaterThanZero == 3) {
                sql += " AND z.weight_count > 0";
            }else if (isGreaterThanZero != null && isGreaterThanZero == 4) {
                sql += " AND z.quantify_count > 0";
            }
            Integer result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, Integer.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
            }
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("查询总量化数条数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    //计算 今日总量化数
    public String todaytotalQuantify(String phone, String startTime){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        try{
            String sql = "select COALESCE(sum(quantify), '0') as todaytotalQuantify from mall_b_users_count WHERE phone <> ?";
            params.add(adminPhone);
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_time) = CURDATE() ";
            }
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        }catch (Exception e){
            return "0";
        }
    }

    // 计算 今日累计量化数
    public String weightCountTotal(String phone, String startTime){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(COALESCE(quantify_count, 0)) AS total_quantify_count\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        quantify_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_time DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count Where phone <> ?";
        params.add(adminPhone);
        try {
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_time) = CURDATE() ";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询累计量化数失败: {}", e.getMessage());
            return "0";
        }
    }
    // 计算 中南惠C的每日所有ID累计量化数
    public String cweightCountTotal(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(sum(quantify), 0) as today_cquantify_count\n" +
                "FROM mall_b_users_count c, mall_b_users u\n" +
                "WHERE c.phone = u.phone\n" +
                "and u.user_type = 'C'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) = CURDATE() ";
        }
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询C用户每日量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    // 计算 中南惠所有B的每日累计量化数
    public String bweightCountTotal(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(sum(quantify), 0) as today_bquantify_count\n" +
                "FROM mall_b_users_count c, mall_b_users u\n" +
                "WHERE c.phone = u.phone\n" +
                "and u.user_type = 'B'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) = CURDATE() ";
        }
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询B用户每日量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    // Admin的每日累计量化数 =  所有合作企业Admain的各ID每日每笔数据量化数比 乘以 今日总合计数
    public void adminDailyQuantity() {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT is_enabled, daily_data_percentage FROM partner_enterprise_admin_data WHERE id = 1";
        String adminDailyQuantity = "0";
        Map<String, Object> dataMap = jdbcTemplate.queryForMap(sql);
        if("0".equals(dataMap.get("is_enabled").toString()) && !"".equals(dataMap.get("daily_data_percentage"))){
            // 今日总合计数
            sql = "SELECT COALESCE(SUM(today_total_count),0) AS today_quantity FROM enterprise_trade_stats WHERE DATE(update_time) = CURDATE()";
            String todayQuantity = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            if(!"0.00".equals(todayQuantity)){
                BigDecimal percentage = new BigDecimal(String.valueOf(dataMap.get("daily_data_percentage")));            // 所有合作企业Admain的各ID每日每笔数据量化数比
                BigDecimal result = new BigDecimal(todayQuantity).multiply(percentage).divide(new BigDecimal(100));  // 今日总合计数 乘以 所有合作企业Admain的各ID每日每笔数据量化数比
                adminDailyQuantity = result.toString();
            }
        }
        // 把adminDailyQuantity 插入到 enterprise_trade_stats 表中,如果当天的数据不存在,则插入,如果存在,则更新
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM mall_b_users_count WHERE phone = ? AND DATE(update_time) = CURDATE() )", Integer.class,adminPhone) == 0) {
            sql = "INSERT INTO mall_b_users_count(phone,quantify,update_time)VALUES(?, ?, CURDATE() )";
            jdbcTemplate.update(sql, adminPhone, adminDailyQuantity);
        }else{
            sql = "UPDATE mall_b_users_count SET quantify = ? WHERE phone = ? AND DATE(update_time) = CURDATE() ";
            jdbcTemplate.update(sql, adminDailyQuantity, adminPhone);
        }
    }

    // 查看 Admin的每日累计量化数
    public String getAdminDailyQuantity(String startTime){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        String yesterdayQuantitySql = "select quantify from mall_b_users_count WHERE phone = ?";
        List<Object> yesTerdayParams = new ArrayList<>();
        yesTerdayParams.add(adminPhone);
        if (startTime != null && !startTime.isEmpty()) {
            yesterdayQuantitySql += " AND DATE(update_time) = ?";
            yesTerdayParams.add(startTime);
        }else{
            yesterdayQuantitySql += " AND DATE(update_time) = CURDATE() ";
        }
        try {
            if(yesTerdayParams.isEmpty()){
                return jdbcTemplate.queryForObject(yesterdayQuantitySql, String.class);
            }else{
                return jdbcTemplate.queryForObject(yesterdayQuantitySql, String.class, yesTerdayParams.toArray());
            }
        } catch (EmptyResultDataAccessException e) {
            return "0";
        }
    }

    /**
     * Admin的累计量化数
     */
    public String adminTotalQuantity(String startTime){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "select quantify_count from mall_b_users_count where phone = ?";
        params.add(adminPhone);
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(update_time) = CURDATE() ";
        }
        try {
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 计算 中南惠C的所有ID累计量化数
     */
    public String cweightCountTotalAllDays(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(ranked.quantify_count) AS total_quantify_count\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        c.quantify_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY c.phone \n" +
                "            ORDER BY c.update_time DESC, c.id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count c,mall_b_users u\n" +
                "    WHERE c.phone = u.phone\n" +
                "    and u.user_type = 'C'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) <= ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询C用户所有ID累计量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    /**
     * 计算 中南惠所有B的累计量化数
     */
    public String bweightCountTotalAlldays(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(ranked.quantify_count) AS total_quantify_count\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        c.quantify_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY c.phone \n" +
                "            ORDER BY c.update_time DESC, c.id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count c,mall_b_users u\n" +
                "    WHERE c.phone = u.phone\n" +
                "    and u.user_type = 'B'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) <= ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询B用户所有ID累计量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    /**
     * 今日总分量
     */
    public String todayTotalWeight(String phone,String startTime){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(SUM(Weight), 0) AS today_total_weight FROM mall_b_users_count WHERE phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(update_time) = CURDATE() ";
        }
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }


    /**
     * 今日累计总分量
     */
    public String totalWeight(String phone,String startTime){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(COALESCE(weight_count, 0)) AS total_weight\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        weight_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_time DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count Where phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(update_time) <= ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(update_time) <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 更新 累计量化数
     */
    public void updateTotalQuantifyCount() {
        String sql = "UPDATE mall_b_users_count t\n" +
                "JOIN (\n" +
                "  SELECT\n" +
                "    c.id,\n" +
                "    SUM(COALESCE(c.quantify, 0)) OVER (PARTITION BY c.phone ORDER BY c.update_time, c.id) AS cum_quantify\n" +
                "  FROM mall_b_users_count c,mall_b_users u\n" +
                "  WHERE c.phone = u.phone\n" +
                "  AND c.update_time <= CURDATE()\n" +
                "  AND u.user_type <> 'C'\n" +
                ") s ON s.id = t.id\n" +
                "SET\n" +
                "  t.quantify_count = COALESCE(s.cum_quantify, 0)\n" +
                "WHERE t.update_time = CURDATE()";
        jdbcTemplate.update(sql);
    }
}
